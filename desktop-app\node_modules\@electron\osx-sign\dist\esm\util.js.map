{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/util.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,eAAe,CAAC;AACvC,OAAO,KAAK,EAAE,MAAM,UAAU,CAAC;AAC/B,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAC5C,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAE7B,OAAO,KAAK,MAAM,OAAO,CAAC;AAG1B,MAAM,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAAC,CAAC;AACnD,QAAQ,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAEzC,MAAM,CAAC,MAAM,SAAS,GAAG,KAAK,CAAC,wBAAwB,CAAC,CAAC;AACzD,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAE3C,MAAM,cAAc,GAAG,UAAU,KAAa;IAC5C,OAAO,KAAK,CAAC,OAAO,CAAC,+BAA+B,EAAE,UAAU,CAAC,EAAE,EAAE;QACnE,OAAO,GAAG,EAAE,KAAK,CAAC;IACpB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,IAAY,EACZ,IAAc,EACd,UAAiC,EAAE;IAEnC,IAAI,QAAQ,CAAC,OAAO,EAAE;QACpB,QAAQ,CACN,cAAc,EACd,IAAI,EACJ,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAClE,CAAC;KACH;IAED,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM;QAC1C,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;YAC/D,IAAI,GAAG,EAAE;gBACP,QAAQ,CAAC,uBAAuB,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;gBACxF,MAAM,CAAC,GAAG,CAAC,CAAC;gBACZ,OAAO;aACR;YACD,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAKD,MAAM,UAAU,oBAAoB,CAAK,IAAiB;IACxD,MAAM,MAAM,GAAQ,EAAE,CAAC;IAEvB,SAAS,cAAc,CAAE,IAAqB;QAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxB,IAAI,IAAI;gBAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7B;aAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,KAAK,MAAM,IAAI,IAAI,IAAI;gBAAE,IAAI,IAAI;oBAAE,cAAc,CAAC,IAAI,CAAC,CAAC;SACzD;IACH,CAAC;IAED,cAAc,CAAC,IAAI,CAAC,CAAC;IACrB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAE,IAAqB;IACvD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;AACzC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAE,IAAqB;IACzD,OAAO,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,sBAAsB,CAAE,IAAqB;IACjE,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACrD,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,CAAC,EAAE;QAC9E,OAAO,QAAQ,CAAC;KACjB;SAAM;QACL,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mBAAmB,CAAE,QAAgB;IAClD,IAAI,MAAM,YAAY,CAAC,QAAQ,CAAC,EAAE;QAChC,OAAO,QAAQ,CAAC;KACjB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe,CAAE,IAAqB;IAC1D,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QACb,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IACD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;KAC7D;IACD,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACpC,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,GAAG,sBAAsB,CAAC,CAAC;KACzE;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CAAE,IAAqB;IAC/D,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACzD,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;aAAM;YACL,SAAS,CAAC,6EAA6E,CAAC,CAAC;SAC1F;KACF;SAAM;QACL,SAAS,CAAC,kEAAkE,CAAC,CAAC;KAC/E;IAED,OAAO,MAAM,sBAAsB,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,SAAS,CAAE,OAAe;IAC9C,QAAQ,CAAC,aAAa,GAAG,OAAO,CAAC,CAAC;IAElC,KAAK,UAAU,UAAU,CAAE,OAAe;QACxC,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3C,OAAO,MAAM,OAAO,CAAC,GAAG,CACtB,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAE9C,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBACjB,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC9B,KAAK,SAAS,EAAE,8CAA8C;wBAC5D,QAAQ,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC;wBACpC,MAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;wBAC1B,OAAO,IAAI,CAAC;oBACd;wBACE,OAAO,MAAM,mBAAmB,CAAC,QAAQ,CAAC,CAAC;iBAC9C;aACF;iBAAM,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;gBACvD,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAC9C,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC9B,KAAK,MAAM,CAAC,CAAC,cAAc;oBAC3B,KAAK,YAAY,EAAE,YAAY;wBAC7B,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC7B;gBACD,OAAO,UAAU,CAAC;aACnB;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC;IAC3C,OAAO,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC"}