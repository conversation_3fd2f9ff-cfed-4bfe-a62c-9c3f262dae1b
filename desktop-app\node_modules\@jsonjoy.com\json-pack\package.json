{"name": "@jsonjoy.com/json-pack", "version": "1.2.0", "description": "High-performance JSON serialization library", "author": {"name": "<PERSON>ich", "url": "https://github.com/streamich"}, "homepage": "https://github.com/jsonjoy-com/json-pack", "repository": "jsonjoy-com/json-pack", "license": "Apache-2.0", "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "keywords": ["json", "cbor", "dag-json", "dag-cbor", "pack", "msgpack", "MessagePack", "json-pack", "<PERSON><PERSON><PERSON><PERSON>", "bencode", "ion", "amazon-ion", "bson", "resp", "resp3", "redis", "resp-3", "resp2"], "engines": {"node": ">=10.0"}, "main": "lib/index.js", "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "files": ["LICENSE", "lib/"], "scripts": {"prettier": "prettier --ignore-path .gitignore --write \"src/**/*.{ts,tsx,js,jsx}\"", "prettier:check": "prettier --ignore-path .gitignore --list-different 'src/**/*.{ts,tsx,js,jsx}'", "lint": "yarn tslint", "tslint": "tslint 'src/**/*.{js,jsx,ts,tsx}' -t verbose --project .", "clean": "rimraf lib typedocs coverage gh-pages yarn-error.log", "build": "tsc --project tsconfig.build.json --module commonjs --target es2020 --outDir lib", "jest": "node -r ts-node/register ./node_modules/.bin/jest", "test": "jest --maxWorkers 7", "test:ci": "yarn jest --maxWorkers 3 --no-cache", "coverage": "yarn test --collectCoverage", "typedoc": "typedoc", "build:pages": "rimraf gh-pages && mkdir -p gh-pages && cp -r typedocs/* gh-pages && cp -r coverage gh-pages/coverage", "deploy:pages": "gh-pages -d gh-pages", "publish-coverage-and-typedocs": "yarn typedoc && yarn coverage && yarn build:pages && yarn deploy:pages"}, "peerDependencies": {"tslib": "2"}, "dependencies": {"@jsonjoy.com/base64": "^1.1.1", "@jsonjoy.com/util": "^1.1.2", "hyperdyperid": "^1.2.0", "thingies": "^1.20.0"}, "devDependencies": {"@msgpack/msgpack": "^3.0.0-beta2", "@redis/client": "^1.5.12", "@shelacek/ubjson": "^1.1.1", "@types/benchmark": "^2.1.2", "@types/jest": "^29.5.12", "app-root-path": "^3.1.0", "axios": "^1.3.5", "base64-js": "^1.5.1", "benchmark": "^2.1.4", "bson": "^5.4.0", "cbor": "^9.0.2", "cbor-js": "^0.1.0", "cbor-sync": "^1.0.4", "cbor-x": "^1.5.9", "cborg": "^2.0.3", "fast-safe-stringify": "^2.1.1", "fast-stable-stringify": "^1.0.0", "fastest-stable-stringify": "^2.0.2", "gh-pages": "^5.0.0", "ion-js": "^4.3.0", "jest": "^29.7.0", "js-base64": "^3.7.2", "jsbi": "^4.3.0", "json-pack-napi": "^0.0.2", "messagepack": "^1.1.12", "msgpack-lite": "^0.1.26", "msgpack5": "^6.0.2", "msgpackr": "^1.6.0", "pako": "^2.0.4", "prettier": "^3.2.5", "pretty-quick": "^3.1.3", "redis-parser": "^3.0.0", "rimraf": "^5.0.0", "safe-stable-stringify": "^2.3.1", "secure-json-parse": "^2.4.0", "tinybench": "^2.4.0", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tslib": "^2.6.2", "tslint": "^6.1.3", "tslint-config-common": "^1.6.2", "typedoc": "^0.25.12", "typescript": "^5.3.3"}, "jest": {"verbose": true, "testEnvironmentOptions": {"url": "http://localhost/"}, "setupFiles": ["<rootDir>/src/__tests__/setup.js"], "moduleFileExtensions": ["ts", "js"], "transform": {"^.+\\.ts$": "ts-jest"}, "transformIgnorePatterns": [], "testRegex": ".*/(__tests__|__jest__|demo)/.*\\.(test|spec)\\.ts$"}, "prettier": {"arrowParens": "always", "printWidth": 120, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "trailingComma": "all", "bracketSpacing": false}}