{"version": 3, "file": "legacy.js", "sourceRoot": "", "sources": ["../src/legacy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,2CAA6B;AAE7B,mCAAgC;AAChC,uCAAkF;AAClF,mDAA+F;AAQ/F,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,0BAA0B,CAAC,CAAC;AAE5C,SAAS,iBAAiB,CAAC,OAAkC;IAC3D,MAAM,IAAI,GAAG,IAAA,+CAA+B,EAAC,OAAO,CAAC,CAAC;IACtD,IAAI,IAAA,2CAA2B,EAAC,IAAI,CAAC,EAAE;QACrC,OAAO,CAAC,IAAI,EAAE,IAAA,oBAAU,EAAC,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,IAAA,oBAAU,EAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;KACjF;SAAM;QACL,OAAO;YACL,UAAU;YACV,IAAA,oBAAU,EAAC,IAAI,CAAC,WAAW,CAAC;YAC5B,aAAa;YACb,IAAA,oBAAU,EAAC,IAAI,CAAC,cAAc,CAAC;SAChC,CAAC;KACH;AACH,CAAC;AAED,SAAsB,mBAAmB,CACvC,IAAgC;;QAEhC,CAAC,CAAC,oCAAoC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACtD,OAAO,MAAM,IAAA,qBAAW,EAAiB,CAAM,GAAG,EAAC,EAAE;YACnD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAChF,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;YACtC,MAAM,SAAS,GAAG,MAAM,IAAA,aAAK,EAC3B,OAAO,EACP,CAAC,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,EACrF;gBACE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;aAChC,CACF,CAAC;YACF,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;gBACxB,MAAM,IAAI,KAAK,CACb,gDAAgD,SAAS,CAAC,IAAI,OAAO,SAAS,CAAC,MAAM,EAAE,CACxF,CAAC;aACH;YACD,CAAC,CAAC,8CAA8C,CAAC,CAAC;YAElD,MAAM,YAAY,GAAG;gBACnB,QAAQ;gBACR,gBAAgB;gBAChB,IAAI;gBACJ,OAAO;gBACP,qBAAqB;gBACrB,IAAI,CAAC,WAAW;gBAChB,GAAG,iBAAiB,CAAC,IAAI,CAAC;aAC3B,CAAC;YAEF,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;aACtD;YAED,MAAM,MAAM,GAAG,MAAM,IAAA,aAAK,EAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAClD,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE;gBACrB,MAAM,IAAI,KAAK,CAAC,2DAA2D,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;aAC7F;YACD,CAAC,CAAC,gBAAgB,CAAC,CAAC;YAEpB,MAAM,SAAS,GAAG,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACjE,IAAI,CAAC,SAAS,EAAE;gBACd,MAAM,IAAI,KAAK,CAAC,6CAA6C,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;aAC/E;YAED,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/B,OAAO;gBACL,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;aACnB,CAAC;QACJ,CAAC,CAAA,CAAC,CAAC;IACL,CAAC;CAAA;AApDD,kDAoDC;AAED,SAAsB,qBAAqB,CAAC,IAA+B;;QACzE,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,MAAM,IAAA,aAAK,EAAC,OAAO,EAAE;YAClC,QAAQ;YACR,qBAAqB;YACrB,IAAI,CAAC,IAAI;YACT,GAAG,iBAAiB,CAAC,IAAI,CAAC;SAC3B,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE;YACrB,+DAA+D;YAC/D,uFAAuF;YACvF,mCAAmC;YACnC,+EAA+E;YAC/E,uCAAuC;YACvC,CAAC,CACC,2EAA2E,IAAI,CAAC,IAAI,OAAO,MAAM,CAAC,MAAM,EAAE,CAC3G,CAAC;YACF,MAAM,IAAA,eAAK,EAAC,KAAK,CAAC,CAAC;YACnB,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC;SACpC;QACD,MAAM,gBAAgB,GAAG,IAAA,+BAAqB,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE9D,IAAI,gBAAgB,CAAC,MAAM,KAAK,aAAa,EAAE;YAC7C,CAAC,CAAC,uCAAuC,CAAC,CAAC;YAC3C,MAAM,IAAA,eAAK,EAAC,KAAK,CAAC,CAAC;YACnB,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC;SACpC;QAED,CAAC,CAAC,6BAA6B,EAAE,gBAAgB,CAAC,CAAC;QAEnD,IAAI,gBAAgB,CAAC,MAAM,KAAK,SAAS,EAAE;YACzC,CAAC,CAAC,qBAAqB,CAAC,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC;;eAEL,gBAAgB,CAAC,UAAU,IAAI,SAAS;WAC5C,gBAAgB,CAAC,aAAa,IAAI,YAAY;QACjD,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC;SACpC;QAED,IAAI,gBAAgB,CAAC,MAAM,KAAK,SAAS,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,sCAAsC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;SACnF;QAED,CAAC,CAAC,6BAA6B,CAAC,CAAC;QACjC,OAAO;IACT,CAAC;CAAA;AA7CD,sDA6CC"}