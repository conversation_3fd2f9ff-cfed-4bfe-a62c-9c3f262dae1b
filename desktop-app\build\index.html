<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' http://localhost:* ws://localhost:*">
  <title>Phone Monitor</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
  <style>
    body {
      margin: 0;
      font-family: 'Roboto', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f5f5f5;
    }
    .loading {
      text-align: center;
      color: #666;
    }
  </style>
</head>
<body>
  <div id="root">
    <div class="loading">
      <h2>Phone Monitor Desktop App</h2>
      <p>Loading... Please ensure the server is running on port 3000.</p>
      <p>This is a placeholder until the React app is properly built.</p>
    </div>
  </div>
</body>
</html>
