{"name": "phone-monitor-desktop", "version": "1.0.0", "description": "Desktop application for phone monitoring system", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.0", "axios": "^1.4.0", "date-fns": "^2.30.0", "electron": "^27.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.0", "recharts": "^2.5.0", "socket.io-client": "^4.6.1"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-react": "^7.22.0", "babel-loader": "^9.1.0", "electron-builder": "^24.6.0", "html-webpack-plugin": "^5.5.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^5.2.2"}, "build": {"appId": "com.phonemonitor.desktop", "productName": "Phone Monitor", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "build/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}