{"version": 3, "file": "NodeFileSystemWritableFileStream.js", "sourceRoot": "", "sources": ["../../src/node-to-fsa/NodeFileSystemWritableFileStream.ts"], "names": [], "mappings": ";;;AAAA,+CAA4C;AAK5C;;;;;;;;GAQG;AACI,MAAM,cAAc,GAAG,KAAK,EACjC,EAAa,EACb,IAAY,EACZ,gBAAyB,EACqB,EAAE;IAChD,IAAI,MAA+B,CAAC;IACpC,IAAI,QAAQ,GAAW,IAAI,GAAG,SAAS,CAAC;IACxC,IAAI,CAAC;QACH,MAAM,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ;YAAE,MAAM,KAAK,CAAC;IAClF,CAAC;IACD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC;gBACjC,MAAM,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAChD,MAAM;YACR,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ;oBAAE,MAAM,KAAK,CAAC;YAClF,CAAC;QACH,CAAC;IACH,CAAC;IACD,IAAI,CAAC,MAAM;QAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,IAAI,CAAC,CAAC;IAC5E,IAAI,gBAAgB;QAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;IAChG,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5B,CAAC,CAAC;AA1BW,QAAA,cAAc,kBA0BzB;AAaF;;;;;;GAMG;AACH,MAAa,gCAAiC,SAAQ,cAAc;IAGlE,YACqB,EAAa,EACb,IAAY,EAC/B,gBAAyB;QAEzB,MAAM,IAAI,GAAa,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAClE,KAAK,CAAC;YACJ,KAAK,CAAC,KAAK;gBACT,MAAM,OAAO,GAAG,IAAA,sBAAc,EAAC,EAAE,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;gBAC3D,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;gBAC3C,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC;gBACzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;gBACrB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;YACvB,CAAC;YACD,KAAK,CAAC,KAAK,CAAC,KAAW;gBACrB,MAAM,IAAI,CAAC,KAAK,CAAC;gBACjB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAI,CAAC,MAAM;oBAAE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;gBAC9C,MAAM,MAAM,GAAG,eAAM,CAAC,IAAI,CACxB,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAC9F,CAAC;gBACF,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnF,IAAI,CAAC,MAAM,IAAI,YAAY,CAAC;YAC9B,CAAC;YACD,KAAK,CAAC,KAAK;gBACT,MAAM,IAAI,CAAC,KAAK,CAAC;gBACjB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAI,CAAC,MAAM;oBAAE,OAAO;gBACpB,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;gBACrB,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5C,CAAC;YACD,KAAK,CAAC,KAAK;gBACT,MAAM,IAAI,CAAC,KAAK,CAAC;gBACjB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAI,CAAC,MAAM;oBAAE,OAAO;gBACpB,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;gBACrB,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;SACF,CAAC,CAAC;QArCgB,OAAE,GAAF,EAAE,CAAW;QACb,SAAI,GAAJ,IAAI,CAAQ;QAqC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAI,CAAC,QAAgB;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,QAAQ,CAAC,IAAY;QAChC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAChC,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QAC9C,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI;YAAE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACvD,CAAC;IAES,KAAK,CAAC,SAAS,CAAC,KAAW;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAOM,KAAK,CAAC,KAAK,CAAC,MAAM;QACvB,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC;QACtE,QAAQ,OAAO,MAAM,EAAE,CAAC;YACtB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAChC,CAAC;YACD,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;gBACvC,QAAQ,WAAW,EAAE,CAAC;oBACpB,KAAK,WAAW,CAAC;oBACjB,KAAK,IAAI,CAAC;oBACV,KAAK,QAAQ;wBACX,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBAChC,OAAO,CAAC,CAAC,CAAC;wBACR,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;4BAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;wBAChC,CAAC;6BAAM,CAAC;4BACN,MAAM,OAAO,GAAG,MAA4C,CAAC;4BAC7D,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gCACrB,KAAK,OAAO,CAAC,CAAC,CAAC;oCACb,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ;wCAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oCAC5E,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gCACrC,CAAC;gCACD,KAAK,UAAU,CAAC,CAAC,CAAC;oCAChB,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;wCAAE,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;oCAC5F,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI;wCAAE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC;oCACnE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gCACpC,CAAC;gCACD,KAAK,MAAM;oCACT,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;wCAAE,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;oCACpG,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gCACpC;oCACE,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAC;4BACpD,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YACD;gBACE,MAAM,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;CACF;AA1HD,4EA0HC"}