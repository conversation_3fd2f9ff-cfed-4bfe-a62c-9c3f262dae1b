{"version": 3, "file": "v5.js", "sourceRoot": "", "sources": ["../../../../src/buffers/utf8/decodeUtf8/v5.ts"], "names": [], "mappings": ";;AAAA,kBAAe,CAAC,KAAiB,EAAE,KAAa,EAAE,MAAc,EAAU,EAAE;IAC1E,MAAM,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC;IAC3B,IAAI,CAAC,GAAG,KAAK,CAAC;IACd,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC;QACf,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,CAAE,CAAC;QACvB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACtB,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAC/B,SAAS;QACX,CAAC;aAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YAChC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QACxE,CAAC;aAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YAChC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QACvG,CAAC;aAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YAChC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC;YAC9B,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC;YAC9B,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC;YAC9B,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACpE,IAAI,IAAI,GAAG,MAAM,EAAE,CAAC;gBAClB,IAAI,IAAI,OAAO,CAAC;gBAChB,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;gBAC7D,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;YACjC,CAAC;YACD,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC"}